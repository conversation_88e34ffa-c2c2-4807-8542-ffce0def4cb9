<view class='container'>
  <navbar isBack='{{isBack}}' home='{{!isBack}}' backgroundColor='{{backgroundColor}}' navTitle='{{navTitle}}'></navbar>
  <view class='bg-color-white info'>
    <van-cell-group>
      <van-field value='{{ info.name }}' label='姓名' required="{{true}}" disabled='{{isEdit}}' input-class="{{!isEdit?'c333':''}}" input-align='right' readonly='{{isEdit}}' placeholder='请输入就诊人姓名' bind:input='nameChange' data-type='name' />
      <van-field value="{{ info.idCard?info.idCard:''}}" type="idcard" label='身份证' required="{{model==0 && !isEdit}}" input-class="{{!isEdit?'c333':''}}" input-align='right' readonly='{{isEdit}}' disabled='{{isEdit}}' placeholder='请输入就诊人身份证{{model==0?"":"（选填）"}}' bind:change='idCardChange' data-type='idCard' maxlength="18" />
      <van-field value="{{ info.gender==1?'男':'女' }}" label='性别' input-align='right' readonly disabled='{{isEdit}}' wx:if='{{isEdit}}' />
      <van-field value='{{ info.birthday }}' label='出生日期' input-align='right' readonly disabled='{{isEdit}}' wx:if='{{isEdit}}' />
      <van-field value='{{ info.phone }}' input-class='c333' type='number' bind:blur='inputChange' data-type='phone' label='手机号码' required="{{true}}" placeholder='就诊人手机号' input-align='right' wx:if='{{model==0}}' maxlength="11">
        <button slot="button" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" class="authbtn btn1 f28">
          微信授权
        </button>
      </van-field>
    </van-cell-group>
    <!--star 儿童就诊人未添加身份证号时候显示 -->
    <van-cell title='性别' wx:if="{{!isEdit && model==1}}" value='{{info.gender==1?"男":"女"}}' is-link bind:click='showAddress' data-type='1' />
    <van-cell title='出生日期' wx:if="{{!isEdit && model==1}}" value='{{info.birthday}}' is-link bind:click='showAddress' data-type='2' />
    <!-- end -->
    <van-cell title='就诊人是您的' value='{{info.relationName}}' required="{{true}}" is-link bind:click='showAddress' data-type='3' />
    <van-cell title="婚姻状况">
      <radio-group value='{{info.maritalStatus}}' bindchange="onMaritalChange">
        <radio value="0" checked="{{info.maritalStatus=='0'}}"><text class="f32 ml10">未婚</text></radio>
        <radio value="1" checked="{{info.maritalStatus=='1'}}" class="ml20"><text class="f32 ml10">已婚</text></radio>
      </radio-group>
    </van-cell>
  </view>
  <!-- 城市信息 -->
  <view class='bg-color-white mt20 info'>
    <AddresPicker label='当前城市' areaValue='{{info.addressPrefix}}' inputAlign='right' required="{{true}}" bind:onPickerConfim='onPickerConfim'>
    </AddresPicker>
  </view>
  <!-- 紧急联系人 成人去掉紧急联系人和电话，儿童不去掉-->
  <view class='bg-color-white mt20 info' wx:if="{{model==1}}">
    <van-field label='紧急联系人姓名' value='{{ info.contactName }}' input-align='right' required="{{true}}" placeholder='请输入' data-key='contactName' bind:input='onInputVal' />
    <van-field label='紧急联系人手机号码' value='{{ info.contactPhone }}' type='number' maxlength="11" input-align='right' required="{{true}}" placeholder='请输入' data-key='contactPhone' bind:input='onInputVal' />
  </view>
  <!-- <view class='bg-color-white mt20 guardian' wx:if='{{model==1}}'>
		<van-cell-group>
			<van-field value='{{ info.guardianName }}' label='监护人姓名' input-align='right' placeholder='请输入监护人姓名'
				bind:blur='inputChange' data-type='guardianName' />
			<van-field value='{{ info.guardianIdCard }}' type="idcard" label='监护人身份证' input-align='right'
				placeholder='请输入监护人身份证' bind:blur='inputChange' maxlength="18" data-type='guardianIdCard' />
			<van-field value='{{ info.guardianPhone }}' label='监护人手机号' type='number' input-align='right'
				placeholder='请输入监护人手机号' bind:blur='inputChange' maxlength="11" data-type='guardianPhone' />
		</van-cell-group>
	</view> -->
  <medicalHistory allergy='{{allergy}}' family='{{family}}' personal='{{personal}}' isDetail='{{isDetail}}' always='{{always}}' bind:propContent='propContent'>
  </medicalHistory>
  <view class="flex p30 agreement">
    <view style="w100 flex">
      <van-checkbox value="{{ checked }}" bind:change="onChange" data-type="checked" icon-size="28rpx" custom-class="dib"><text class="c666 f24">我已阅读并同意</text><text class="f24 color-primary" data-type='11' catchtap='goAgreement'>《用户协议》</text><text class="f24 color-primary" data-type='12' catchtap='goAgreement'>《隐私政策》</text>
      </van-checkbox>
    </view>
  </view>
  <view class='fixed b0 l0 w100 bg-color-white p30 flex_m'>
    <view class='del f28 c999 flex_c_m' bindtap='del'>删除</view>
    <view class='save f28 cfff flex_c_m ml30 bg-color-primary' bindtap='save'>保存</view>
  </view>
  <van-popup show='{{showPicker}}' bind:close='showAddress' close-on-click-overlay='{{true}}' round='{{true}}' position='bottom'>
    <view class='relationPicker' catchtap='return' wx:if="{{relationBoxShow}}">
      <van-picker columns='{{ relationList }}' show-toolbar title='与就诊人关系' bind:cancel='onCancel' bind:confirm='confirRelation' value-key='relationName' wx:if='{{model==0}}' />
      <van-picker columns='{{ relationListChild }}' show-toolbar title='与就诊人关系' bind:cancel='onCancel' bind:confirm='confirRelation' value-key='relationName' wx:if='{{model==1}}' />
    </view>
    <view class="timeBox" catchtap="return" wx:if="{{timeBoxShow}}">
      <van-datetime-picker type="date" value="{{ currentDate }}" bind:confirm="confirBrithday" bind:cancel="onCancel" max-date="{{ maxDate }}" min-date="{{ minDate }}" formatter="{{ formatter }}" />
    </view>
    <view class='relationPicker' catchtap='return' wx:if="{{sexBoxShow}}">
      <van-picker columns='{{ sexList }}' show-toolbar title='性别' bind:cancel='onCancel' bind:confirm='confirSex' value-key='name' />
    </view>
  </van-popup>
</view>